[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
	hooksPath = .husky/_
[remote "origin"]
	url = https://github.com/LaxBloxBoy2/cubent-extension.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[lfs]
	repositoryformatversion = 0
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
[branch "fix/account-view-crash-and-auth-system"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/fix/account-view-crash-and-auth-system
[branch "fix/hide-announcement-center-chat-input"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/fix/hide-announcement-center-chat-input
[branch "feat/persistent-usage-analytics"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feat/persistent-usage-analytics
[branch "feat/chat-input-darker-styling"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feat/chat-input-darker-styling
[branch "reactive-change-tracking"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/reactive-change-tracking
[branch "fix/diff-display-accuracy"]
	vscode-merge-base = origin/main
[branch "fix/diffbar-visibility"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/fix/diffbar-visibility
[branch "ui-improvements"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/ui-improvements
[branch "update-extension-ui"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/update-extension-ui
[branch "fix/trial-banner-dashboard"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/fix/trial-banner-dashboard
[branch "feat/api-key-manager-base-url-toggles"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feat/api-key-manager-base-url-toggles
[branch "ui-improvements-status-buttons"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/ui-improvements-status-buttons
