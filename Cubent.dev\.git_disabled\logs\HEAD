********************************00000000 5925c8ee0c0712d427166d538dfacff00851a991 LaxBloxBoy2 <<EMAIL>> 1751731982 +0200	commit (initial): Initial commit: Fresh Cubent VS Code extension repository
5925c8ee0c0712d427166d538dfacff00851a991 ********************************00000000 LaxBloxBoy2 <<EMAIL>> ********** +0200	Branch: renamed refs/heads/master to refs/heads/main
********************************00000000 5925c8ee0c0712d427166d538dfacff00851a991 LaxBloxBoy2 <<EMAIL>> ********** +0200	Branch: renamed refs/heads/master to refs/heads/main
5925c8ee0c0712d427166d538dfacff00851a991 01c8c2d48b3550914bede7b1eda2413aa4b230c2 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Complete README rewrite with unique content and updated repository URLs
01c8c2d48b3550914bede7b1eda2413aa4b230c2 01c8c2d48b3550914bede7b1eda2413aa4b230c2 LaxBloxBoy2 <<EMAIL>> ********** +0200	checkout: moving from main to fix/account-view-crash-and-auth-system
01c8c2d48b3550914bede7b1eda2413aa4b230c2 0251fc0236e29f4dad5a55040021755e17b897eb LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix AccountView crash and implement authentication system
0251fc0236e29f4dad5a55040021755e17b897eb 01c8c2d48b3550914bede7b1eda2413aa4b230c2 LaxBloxBoy2 <<EMAIL>> ********** +0200	checkout: moving from fix/account-view-crash-and-auth-system to main
01c8c2d48b3550914bede7b1eda2413aa4b230c2 0251fc0236e29f4dad5a55040021755e17b897eb LaxBloxBoy2 <<EMAIL>> ********** +0200	merge fix/account-view-crash-and-auth-system: Fast-forward
0251fc0236e29f4dad5a55040021755e17b897eb 0251fc0236e29f4dad5a55040021755e17b897eb LaxBloxBoy2 <<EMAIL>> ********** +0200	checkout: moving from main to fix/hide-announcement-center-chat-input
0251fc0236e29f4dad5a55040021755e17b897eb 54c353df6bd2614706d5cb30c0db44db0411ef6d LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix: Hide announcement popup and center chat input
54c353df6bd2614706d5cb30c0db44db0411ef6d f0ae5f8025166bd0aff6028ff24dc0bad40051c3 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update login flow UI to match usage/requests background and add orange accents
f0ae5f8025166bd0aff6028ff24dc0bad40051c3 2d84bb034d3cc56ecdfac869ea67542071a0d407 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Improve chat UI positioning and icon styling
2d84bb034d3cc56ecdfac869ea67542071a0d407 7fcd79f7fd83e736b151c629d3aa5612f1672208 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Build and install current version without usage analytics feature
7fcd79f7fd83e736b151c629d3aa5612f1672208 e5995298b870e5dc1ecfa3b5002b760f2b2f8465 LaxBloxBoy2 <<EMAIL>> 1751980459 +0200	commit: Add comment to FeedbackButtons component and rebuild extension
e5995298b870e5dc1ecfa3b5002b760f2b2f8465 0251fc0236e29f4dad5a55040021755e17b897eb LaxBloxBoy2 <<EMAIL>> 1751980550 +0200	checkout: moving from fix/hide-announcement-center-chat-input to main
0251fc0236e29f4dad5a55040021755e17b897eb e5995298b870e5dc1ecfa3b5002b760f2b2f8465 LaxBloxBoy2 <<EMAIL>> 1751980558 +0200	merge fix/hide-announcement-center-chat-input: Fast-forward
e5995298b870e5dc1ecfa3b5002b760f2b2f8465 e5995298b870e5dc1ecfa3b5002b760f2b2f8465 LaxBloxBoy2 <<EMAIL>> 1751993217 +0200	checkout: moving from main to feat/persistent-usage-analytics
e5995298b870e5dc1ecfa3b5002b760f2b2f8465 c1094b3c4a02d619ae9c8c4877b8d6c98f042357 LaxBloxBoy2 <<EMAIL>> 1751993264 +0200	commit: feat: Implement persistent usage analytics with improved UI
c1094b3c4a02d619ae9c8c4877b8d6c98f042357 bb1754de69c793b1ccf4078964b6c40ae3b08b3f LaxBloxBoy2 <<EMAIL>> 1751997504 +0200	commit: feat: improve tool UI consistency and fix layout issues
bb1754de69c793b1ccf4078964b6c40ae3b08b3f e5995298b870e5dc1ecfa3b5002b760f2b2f8465 LaxBloxBoy2 <<EMAIL>> 1751997600 +0200	checkout: moving from feat/persistent-usage-analytics to main
e5995298b870e5dc1ecfa3b5002b760f2b2f8465 bb1754de69c793b1ccf4078964b6c40ae3b08b3f LaxBloxBoy2 <<EMAIL>> 1751997617 +0200	merge feat/persistent-usage-analytics: Fast-forward
bb1754de69c793b1ccf4078964b6c40ae3b08b3f bb1754de69c793b1ccf4078964b6c40ae3b08b3f LaxBloxBoy2 <<EMAIL>> 1752017603 +0200	checkout: moving from main to feat/chat-input-darker-styling
bb1754de69c793b1ccf4078964b6c40ae3b08b3f 2f210c5575feeda2398b4432d2ca0253a2d231bd LaxBloxBoy2 <<EMAIL>> 1752017643 +0200	commit: feat: improve chat input styling with darker background and enhanced toolbar
2f210c5575feeda2398b4432d2ca0253a2d231bd fd1c91207f7d1fb284354effbedbe5d2b5743b83 LaxBloxBoy2 <<EMAIL>> 1752074996 +0200	commit: feat: update mode and model selectors to use consistent grey background
fd1c91207f7d1fb284354effbedbe5d2b5743b83 bb1754de69c793b1ccf4078964b6c40ae3b08b3f LaxBloxBoy2 <<EMAIL>> 1752075303 +0200	checkout: moving from feat/chat-input-darker-styling to main
bb1754de69c793b1ccf4078964b6c40ae3b08b3f fd1c91207f7d1fb284354effbedbe5d2b5743b83 LaxBloxBoy2 <<EMAIL>> 1752075309 +0200	merge feat/chat-input-darker-styling: Fast-forward
fd1c91207f7d1fb284354effbedbe5d2b5743b83 fd1c91207f7d1fb284354effbedbe5d2b5743b83 LaxBloxBoy2 <<EMAIL>> 1752094114 +0200	checkout: moving from main to reactive-change-tracking
fd1c91207f7d1fb284354effbedbe5d2b5743b83 139eacc18b2d023b5b32387609e70660d2d4d12b LaxBloxBoy2 <<EMAIL>> 1752094185 +0200	commit: Implement reactive change tracking system
139eacc18b2d023b5b32387609e70660d2d4d12b 1e95bac9899c7cc086e9d6c742ad2d80eda13382 LaxBloxBoy2 <<EMAIL>> 1752103718 +0200	commit: Fix reactive tracking restart and cross-chat issues
1e95bac9899c7cc086e9d6c742ad2d80eda13382 fd1c91207f7d1fb284354effbedbe5d2b5743b83 LaxBloxBoy2 <<EMAIL>> 1752103724 +0200	checkout: moving from reactive-change-tracking to main
fd1c91207f7d1fb284354effbedbe5d2b5743b83 1e95bac9899c7cc086e9d6c742ad2d80eda13382 LaxBloxBoy2 <<EMAIL>> 1752103730 +0200	merge reactive-change-tracking: Fast-forward
1e95bac9899c7cc086e9d6c742ad2d80eda13382 1e95bac9899c7cc086e9d6c742ad2d80eda13382 LaxBloxBoy2 <<EMAIL>> 1752105400 +0200	checkout: moving from main to fix/diff-display-accuracy
1e95bac9899c7cc086e9d6c742ad2d80eda13382 6d1b2092e46cbd2fc2808817bd94f1a2bb1294b3 LaxBloxBoy2 <<EMAIL>> 1752105438 +0200	commit: Fix diff display accuracy in tools and diffbar
6d1b2092e46cbd2fc2808817bd94f1a2bb1294b3 1b55afec34eab2e20fa84949315e1071fbb176ba LaxBloxBoy2 <<EMAIL>> 1752106757 +0200	commit: Fix context background transparency and diffbar line counting accuracy
1b55afec34eab2e20fa84949315e1071fbb176ba 5d2b0534faef0f1c7870b8e8af2396d50ab12691 LaxBloxBoy2 <<EMAIL>> 1752151287 +0200	commit: Fix token counting and diff line counting issues
5d2b0534faef0f1c7870b8e8af2396d50ab12691 1e95bac9899c7cc086e9d6c742ad2d80eda13382 LaxBloxBoy2 <<EMAIL>> 1752151367 +0200	checkout: moving from fix/diff-display-accuracy to main
1e95bac9899c7cc086e9d6c742ad2d80eda13382 5d2b0534faef0f1c7870b8e8af2396d50ab12691 LaxBloxBoy2 <<EMAIL>> 1752151375 +0200	merge fix/diff-display-accuracy: Fast-forward
5d2b0534faef0f1c7870b8e8af2396d50ab12691 5d2b0534faef0f1c7870b8e8af2396d50ab12691 LaxBloxBoy2 <<EMAIL>> 1752156580 +0200	checkout: moving from main to fix/diffbar-visibility
5d2b0534faef0f1c7870b8e8af2396d50ab12691 5f59ecc2b5fe73af208025a3da2a29af3e1da844 LaxBloxBoy2 <<EMAIL>> 1752156639 +0200	commit: Fix diffbar visibility: hide on homepage and show only during active tasks
5f59ecc2b5fe73af208025a3da2a29af3e1da844 8ff8adc465512ffa98bcbc852061d6ff77f51ce2 LaxBloxBoy2 <<EMAIL>> 1752164151 +0200	commit: feat: Add comprehensive chat history management system
8ff8adc465512ffa98bcbc852061d6ff77f51ce2 777bd52c143e4c54dec4ff906e4c2456458066d9 LaxBloxBoy2 <<EMAIL>> 1752167331 +0200	commit: feat: Implement compact error display and hide retry text
777bd52c143e4c54dec4ff906e4c2456458066d9 13123bd8560664995ce83cbc148f92ec819d6a2a LaxBloxBoy2 <<EMAIL>> 1752182432 +0200	commit: Add TODO comment for error formatting system fix
13123bd8560664995ce83cbc148f92ec819d6a2a 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752189259 +0200	commit: Hide About Cubent Code section from settings interface
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 5d2b0534faef0f1c7870b8e8af2396d50ab12691 LaxBloxBoy2 <<EMAIL>> 1752189264 +0200	checkout: moving from fix/diffbar-visibility to main
5d2b0534faef0f1c7870b8e8af2396d50ab12691 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752189269 +0200	merge fix/diffbar-visibility: Fast-forward
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752189287 +0200	checkout: moving from main to fix/diffbar-visibility
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752189439 +0200	checkout: moving from fix/diffbar-visibility to main
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752190385 +0200	checkout: moving from main to ui-improvements
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 0c10b8cc98057e485b0f6a6cf0f5843648f2692c LaxBloxBoy2 <<EMAIL>> 1752190415 +0200	commit: UI improvements and settings updates
0c10b8cc98057e485b0f6a6cf0f5843648f2692c 8f5d56c51aa7712f9ccb788ff4582dc63fce4c7d LaxBloxBoy2 <<EMAIL>> 1752190732 +0200	commit: Hide UI elements as requested: profile management icons, API key inputs for BYAK profiles, diff settings, checkpoints section, and update save/close buttons design
8f5d56c51aa7712f9ccb788ff4582dc63fce4c7d 95a7cfb024fa0a09a64a186c15d545b00ae49547 LaxBloxBoy2 <<EMAIL>> 1752592438 +0200	commit: feat: Add compact MCP resource display with same design as tool display
95a7cfb024fa0a09a64a186c15d545b00ae49547 48680684bfd3e5b7addb6ff748804842e198b97a LaxBloxBoy2 <<EMAIL>> 1752620084 +0200	commit: Redesign General Settings to match reference design
48680684bfd3e5b7addb6ff748804842e198b97a 3d8aeff5522bb28a2c22dbce00ddb053de87b92c LaxBloxBoy2 <<EMAIL>> 1752628194 +0200	commit: feat: update toggle switches and MCP settings UI
3d8aeff5522bb28a2c22dbce00ddb053de87b92c c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> 1752628355 +0200	commit: feat: update toggle switches and MCP settings UI
c2c9665301414a40dfb671586ab6568777975d3b 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752628441 +0200	checkout: moving from ui-improvements to main
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> ********** +0200	merge ui-improvements: Fast-forward
c2c9665301414a40dfb671586ab6568777975d3b c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> ********** +0200	checkout: moving from main to update-extension-ui
c2c9665301414a40dfb671586ab6568777975d3b e12c3c9b55569c231b671a3d2d105d74c13d2281 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update VSCode extension: hide account tab, remove brain icon, fix profile dropdown positioning
e12c3c9b55569c231b671a3d2d105d74c13d2281 eb9fda511bae78476956039326b44bb867995926 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix hidden profiles not persisting in settings
eb9fda511bae78476956039326b44bb867995926 5be7524f6cff00fd9d8d5fe23015d99e16414855 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Add search functionality to API Key Management and improve Built In Models UI
5be7524f6cff00fd9d8d5fe23015d99e16414855 05614db63b1cb00720eeaecd4c38df4cde00d737 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Replace hardcoded colors with VSCode theme variables in settings components
05614db63b1cb00720eeaecd4c38df4cde00d737 548130ebfb291ee3ae85136a2f03a671ca176200 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Replace hardcoded colors with VSCode theme variables in settings tabs and chat tools
548130ebfb291ee3ae85136a2f03a671ca176200 0af876a326f9c1746a59ff0d183cafaa909e011d LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix hiddenProfiles persistence by using profile names instead of IDs
0af876a326f9c1746a59ff0d183cafaa909e011d c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> 1752776423 +0200	checkout: moving from update-extension-ui to main
c2c9665301414a40dfb671586ab6568777975d3b 0af876a326f9c1746a59ff0d183cafaa909e011d LaxBloxBoy2 <<EMAIL>> 1752776431 +0200	merge update-extension-ui: Fast-forward
0af876a326f9c1746a59ff0d183cafaa909e011d 0af876a326f9c1746a59ff0d183cafaa909e011d LaxBloxBoy2 <<EMAIL>> 1752777167 +0200	checkout: moving from main to fix/trial-banner-dashboard
0af876a326f9c1746a59ff0d183cafaa909e011d 275af5cad2ffb1edfa3601b8c7f5e391ede6c26d LaxBloxBoy2 <<EMAIL>> 1752889960 +0200	commit: Reduce chat row and message padding for more compact interface
275af5cad2ffb1edfa3601b8c7f5e391ede6c26d 0af876a326f9c1746a59ff0d183cafaa909e011d LaxBloxBoy2 <<EMAIL>> 1752891174 +0200	checkout: moving from fix/trial-banner-dashboard to main
0af876a326f9c1746a59ff0d183cafaa909e011d 275af5cad2ffb1edfa3601b8c7f5e391ede6c26d LaxBloxBoy2 <<EMAIL>> 1752891182 +0200	merge fix/trial-banner-dashboard: Fast-forward
275af5cad2ffb1edfa3601b8c7f5e391ede6c26d 275af5cad2ffb1edfa3601b8c7f5e391ede6c26d LaxBloxBoy2 <<EMAIL>> 1752938508 +0200	checkout: moving from main to feat/api-key-manager-base-url-toggles
275af5cad2ffb1edfa3601b8c7f5e391ede6c26d e0b486c694a004b316bc1c7b73b3130827b245e5 LaxBloxBoy2 <<EMAIL>> 1752938562 +0200	commit: feat: Add custom base URL toggles to API Key Manager popup
e0b486c694a004b316bc1c7b73b3130827b245e5 cb5dedaacc5cc44cc2330723e13ca7a11f57c71d LaxBloxBoy2 <<EMAIL>> 1752971401 +0200	commit: Fix UI styling and duplicate thinking text issues
cb5dedaacc5cc44cc2330723e13ca7a11f57c71d 7c219f16cb6c754415fe69e718edc85b5d0c6cab LaxBloxBoy2 <<EMAIL>> 1753063631 +0200	commit: Add comment to package.json and implement autocomplete features with usage tracking
7c219f16cb6c754415fe69e718edc85b5d0c6cab 890ab59ed995c4ecd75300ca635dbfe036cf9e9f LaxBloxBoy2 <<EMAIL>> 1753063763 +0200	commit: Add remaining UI components and settings updates
890ab59ed995c4ecd75300ca635dbfe036cf9e9f 4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 LaxBloxBoy2 <<EMAIL>> 1753066835 +0200	commit: feat: Add web dashboard autocomplete tracking integration
4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 LaxBloxBoy2 <<EMAIL>> 1753072422 +0200	checkout: moving from feat/api-key-manager-base-url-toggles to feat/api-key-manager-base-url-toggles
4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 37dd247fb44556eb9223156954066f3ee1a6b380 LaxBloxBoy2 <<EMAIL>> 1753072807 +0200	commit: fix: Implement proper autocomplete usage tracking
37dd247fb44556eb9223156954066f3ee1a6b380 ebd8ce3b34628fb8b55f98c04f5d251a0698dfc3 LaxBloxBoy2 <<EMAIL>> 1753118843 +0200	commit: feat: improve UI and context menu titles
ebd8ce3b34628fb8b55f98c04f5d251a0698dfc3 25793bc7ceeb092de1d3ff1f91a609126a744383 LaxBloxBoy2 <<EMAIL>> 1753123944 +0200	commit: feat: enhance API Key & Models Management interface
25793bc7ceeb092de1d3ff1f91a609126a744383 b1aeac82483ce8ef73a59ca61a272e3b48819a7f LaxBloxBoy2 <<EMAIL>> 1753197308 +0200	commit: feat: improve subscription error handling and task management
b1aeac82483ce8ef73a59ca61a272e3b48819a7f 497169a1052b6bd7b16661a8420af8ecd22f1ffb LaxBloxBoy2 <<EMAIL>> 1753218904 +0200	commit: fix: update homepage hero buttons - remove JetBrains, add lock icon to VS Code
497169a1052b6bd7b16661a8420af8ecd22f1ffb 0d24309160620b7fe250cd423f1711daac00d5fc LaxBloxBoy2 <<EMAIL>> 1753234786 +0200	commit: Update autocomplete settings and settings view
0d24309160620b7fe250cd423f1711daac00d5fc e3c6a814aef6883a33acb58213c26436c7255754 LaxBloxBoy2 <<EMAIL>> 1753237895 +0200	commit: Update extension build and API configuration components
e3c6a814aef6883a33acb58213c26436c7255754 46f0f03d16819d03395a666dd2ef2cd96decc552 LaxBloxBoy2 <<EMAIL>> 1753240092 +0200	commit: Fix UI consistency issues and improve default user experience
46f0f03d16819d03395a666dd2ef2cd96decc552 b07f504149d9a4dfc621e825059d5a7b4f96835b LaxBloxBoy2 <<EMAIL>> 1753322922 +0200	commit: Fix OpenRouter model selection persistence and profile ID reference issues
b07f504149d9a4dfc621e825059d5a7b4f96835b a5e6230cf78e9715a83723a828ee5af62f36a406 LaxBloxBoy2 <<EMAIL>> 1753388261 +0200	commit: feat: add workspace action buttons when no workspace is open
a5e6230cf78e9715a83723a828ee5af62f36a406 7fc956cee9917402c147d78e2d3a1b0c4ccc5cf2 LaxBloxBoy2 <<EMAIL>> 1753401310 +0200	commit: Fix critical BYOK plan enforcement and Stripe webhook bugs
7fc956cee9917402c147d78e2d3a1b0c4ccc5cf2 68ce548642bbf4bfbc45308be9b7839eb984bc75 LaxBloxBoy2 <<EMAIL>> 1753464694 +0200	commit: Fix authentication and webview message handling
68ce548642bbf4bfbc45308be9b7839eb984bc75 2fa8c280ab75778a39d49a466360c64dbb9a3eb4 LaxBloxBoy2 <<EMAIL>> 1753465196 +0200	commit: Fix ChatRow height calculation issue causing messages to not display
2fa8c280ab75778a39d49a466360c64dbb9a3eb4 147b1c1a389f53b306d44cdb26a4f5578c3a9313 LaxBloxBoy2 <<EMAIL>> 1753467294 +0200	commit: Fix OpenRouter profile creation bug preventing profile override
147b1c1a389f53b306d44cdb26a4f5578c3a9313 147b1c1a389f53b306d44cdb26a4f5578c3a9313 LaxBloxBoy2 <<EMAIL>> 1753476885 +0200	checkout: moving from feat/api-key-manager-base-url-toggles to feat/api-key-manager-base-url-toggles
147b1c1a389f53b306d44cdb26a4f5578c3a9313 0419d083c18dbb4d62fc1103cd923200b55af03f LaxBloxBoy2 <<EMAIL>> 1753496889 +0200	commit: feat: hide OpenRouter profile when API key is not configured
0419d083c18dbb4d62fc1103cd923200b55af03f 275af5cad2ffb1edfa3601b8c7f5e391ede6c26d LaxBloxBoy2 <<EMAIL>> 1753497111 +0200	checkout: moving from feat/api-key-manager-base-url-toggles to main
275af5cad2ffb1edfa3601b8c7f5e391ede6c26d a5e6230cf78e9715a83723a828ee5af62f36a406 LaxBloxBoy2 <<EMAIL>> 1753497121 +0200	pull: Fast-forward
a5e6230cf78e9715a83723a828ee5af62f36a406 0419d083c18dbb4d62fc1103cd923200b55af03f LaxBloxBoy2 <<EMAIL>> 1753497129 +0200	merge feat/api-key-manager-base-url-toggles: Fast-forward
0419d083c18dbb4d62fc1103cd923200b55af03f 0419d083c18dbb4d62fc1103cd923200b55af03f LaxBloxBoy2 <<EMAIL>> 1753556445 +0200	checkout: moving from main to ui-improvements-status-buttons
0419d083c18dbb4d62fc1103cd923200b55af03f f89027a2d77f6db4f63a9a81c7fc2ce7807fcc0a LaxBloxBoy2 <<EMAIL>> 1753556489 +0200	commit: UI improvements: Add approval buttons to switchMode, update status indicators, and improve OpenRouter layout
f89027a2d77f6db4f63a9a81c7fc2ce7807fcc0a 12e1a268d74b60c3c0e0374f7cf240d223d3cd4e LaxBloxBoy2 <<EMAIL>> 1753678006 +0200	commit: feat: Improve first-time setup and model management
12e1a268d74b60c3c0e0374f7cf240d223d3cd4e 5c0358acba0abba41d99c24e6f7eb25401a46168 LaxBloxBoy2 <<EMAIL>> 1753727330 +0200	commit: Update extension branding and marketplace assets
5c0358acba0abba41d99c24e6f7eb25401a46168 024fe2a0f3412723c284e84ef870bff9bb48b09e LaxBloxBoy2 <<EMAIL>> 1753737962 +0200	commit: Update extension to v0.30.8: new icon, external images, 30 keywords, improved README formatting
024fe2a0f3412723c284e84ef870bff9bb48b09e b770c30c2988b6850687821f9d44aaf5fee0e10a LaxBloxBoy2 <<EMAIL>> 1753802322 +0200	commit: Update version to 0.31.5 and fix webview build with proper assets
b770c30c2988b6850687821f9d44aaf5fee0e10a 9520f32ec90d695dffb85ffe2941386d0d4cd2da LaxBloxBoy2 <<EMAIL>> 1753852226 +0200	commit: chore: bump version to 0.31.7 and restore welcome screen
