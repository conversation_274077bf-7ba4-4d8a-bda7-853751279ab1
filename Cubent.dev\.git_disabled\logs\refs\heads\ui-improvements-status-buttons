0000000000000000000000000000000000000000 0419d083c18dbb4d62fc1103cd923200b55af03f LaxBloxBoy2 <<EMAIL>> 1753556445 +0200	branch: Created from HEAD
0419d083c18dbb4d62fc1103cd923200b55af03f f89027a2d77f6db4f63a9a81c7fc2ce7807fcc0a LaxBloxBoy2 <<EMAIL>> 1753556489 +0200	commit: UI improvements: Add approval buttons to switchMode, update status indicators, and improve OpenRouter layout
f89027a2d77f6db4f63a9a81c7fc2ce7807fcc0a 12e1a268d74b60c3c0e0374f7cf240d223d3cd4e LaxBloxBoy2 <<EMAIL>> 1753678006 +0200	commit: feat: Improve first-time setup and model management
12e1a268d74b60c3c0e0374f7cf240d223d3cd4e 5c0358acba0abba41d99c24e6f7eb25401a46168 LaxBloxBoy2 <<EMAIL>> 1753727330 +0200	commit: Update extension branding and marketplace assets
5c0358acba0abba41d99c24e6f7eb25401a46168 024fe2a0f3412723c284e84ef870bff9bb48b09e LaxBloxBoy2 <<EMAIL>> 1753737962 +0200	commit: Update extension to v0.30.8: new icon, external images, 30 keywords, improved README formatting
024fe2a0f3412723c284e84ef870bff9bb48b09e b770c30c2988b6850687821f9d44aaf5fee0e10a LaxBloxBoy2 <<EMAIL>> 1753802322 +0200	commit: Update version to 0.31.5 and fix webview build with proper assets
b770c30c2988b6850687821f9d44aaf5fee0e10a 9520f32ec90d695dffb85ffe2941386d0d4cd2da LaxBloxBoy2 <<EMAIL>> 1753852226 +0200	commit: chore: bump version to 0.31.7 and restore welcome screen
