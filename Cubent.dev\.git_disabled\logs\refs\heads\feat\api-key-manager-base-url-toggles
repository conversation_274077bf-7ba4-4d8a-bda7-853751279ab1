0000000000000000000000000000000000000000 275af5cad2ffb1edfa3601b8c7f5e391ede6c26d LaxBloxBoy2 <<EMAIL>> 1752938508 +0200	branch: Created from HEAD
275af5cad2ffb1edfa3601b8c7f5e391ede6c26d e0b486c694a004b316bc1c7b73b3130827b245e5 LaxBloxBoy2 <<EMAIL>> 1752938562 +0200	commit: feat: Add custom base URL toggles to API Key Manager popup
e0b486c694a004b316bc1c7b73b3130827b245e5 cb5dedaacc5cc44cc2330723e13ca7a11f57c71d LaxBloxBoy2 <<EMAIL>> 1752971401 +0200	commit: Fix UI styling and duplicate thinking text issues
cb5dedaacc5cc44cc2330723e13ca7a11f57c71d 7c219f16cb6c754415fe69e718edc85b5d0c6cab LaxBloxBoy2 <<EMAIL>> 1753063631 +0200	commit: Add comment to package.json and implement autocomplete features with usage tracking
7c219f16cb6c754415fe69e718edc85b5d0c6cab 890ab59ed995c4ecd75300ca635dbfe036cf9e9f LaxBloxBoy2 <<EMAIL>> 1753063763 +0200	commit: Add remaining UI components and settings updates
890ab59ed995c4ecd75300ca635dbfe036cf9e9f 4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 LaxBloxBoy2 <<EMAIL>> 1753066835 +0200	commit: feat: Add web dashboard autocomplete tracking integration
4c9662383d55b425f10e5bb07b9d3c1a1c7e9006 37dd247fb44556eb9223156954066f3ee1a6b380 LaxBloxBoy2 <<EMAIL>> 1753072807 +0200	commit: fix: Implement proper autocomplete usage tracking
37dd247fb44556eb9223156954066f3ee1a6b380 ebd8ce3b34628fb8b55f98c04f5d251a0698dfc3 LaxBloxBoy2 <<EMAIL>> 1753118843 +0200	commit: feat: improve UI and context menu titles
ebd8ce3b34628fb8b55f98c04f5d251a0698dfc3 25793bc7ceeb092de1d3ff1f91a609126a744383 LaxBloxBoy2 <<EMAIL>> 1753123944 +0200	commit: feat: enhance API Key & Models Management interface
25793bc7ceeb092de1d3ff1f91a609126a744383 b1aeac82483ce8ef73a59ca61a272e3b48819a7f LaxBloxBoy2 <<EMAIL>> 1753197308 +0200	commit: feat: improve subscription error handling and task management
b1aeac82483ce8ef73a59ca61a272e3b48819a7f 497169a1052b6bd7b16661a8420af8ecd22f1ffb LaxBloxBoy2 <<EMAIL>> 1753218904 +0200	commit: fix: update homepage hero buttons - remove JetBrains, add lock icon to VS Code
497169a1052b6bd7b16661a8420af8ecd22f1ffb 0d24309160620b7fe250cd423f1711daac00d5fc LaxBloxBoy2 <<EMAIL>> 1753234786 +0200	commit: Update autocomplete settings and settings view
0d24309160620b7fe250cd423f1711daac00d5fc e3c6a814aef6883a33acb58213c26436c7255754 LaxBloxBoy2 <<EMAIL>> 1753237895 +0200	commit: Update extension build and API configuration components
e3c6a814aef6883a33acb58213c26436c7255754 46f0f03d16819d03395a666dd2ef2cd96decc552 LaxBloxBoy2 <<EMAIL>> 1753240092 +0200	commit: Fix UI consistency issues and improve default user experience
46f0f03d16819d03395a666dd2ef2cd96decc552 b07f504149d9a4dfc621e825059d5a7b4f96835b LaxBloxBoy2 <<EMAIL>> 1753322922 +0200	commit: Fix OpenRouter model selection persistence and profile ID reference issues
b07f504149d9a4dfc621e825059d5a7b4f96835b a5e6230cf78e9715a83723a828ee5af62f36a406 LaxBloxBoy2 <<EMAIL>> 1753388261 +0200	commit: feat: add workspace action buttons when no workspace is open
a5e6230cf78e9715a83723a828ee5af62f36a406 7fc956cee9917402c147d78e2d3a1b0c4ccc5cf2 LaxBloxBoy2 <<EMAIL>> 1753401310 +0200	commit: Fix critical BYOK plan enforcement and Stripe webhook bugs
7fc956cee9917402c147d78e2d3a1b0c4ccc5cf2 68ce548642bbf4bfbc45308be9b7839eb984bc75 LaxBloxBoy2 <<EMAIL>> 1753464694 +0200	commit: Fix authentication and webview message handling
68ce548642bbf4bfbc45308be9b7839eb984bc75 2fa8c280ab75778a39d49a466360c64dbb9a3eb4 LaxBloxBoy2 <<EMAIL>> 1753465196 +0200	commit: Fix ChatRow height calculation issue causing messages to not display
2fa8c280ab75778a39d49a466360c64dbb9a3eb4 147b1c1a389f53b306d44cdb26a4f5578c3a9313 LaxBloxBoy2 <<EMAIL>> 1753467294 +0200	commit: Fix OpenRouter profile creation bug preventing profile override
147b1c1a389f53b306d44cdb26a4f5578c3a9313 0419d083c18dbb4d62fc1103cd923200b55af03f LaxBloxBoy2 <<EMAIL>> 1753496889 +0200	commit: feat: hide OpenRouter profile when API key is not configured
