0000000000000000000000000000000000000000 c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> ********** +0200	branch: Created from HEAD
c2c9665301414a40dfb671586ab6568777975d3b e12c3c9b55569c231b671a3d2d105d74c13d2281 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update VSCode extension: hide account tab, remove brain icon, fix profile dropdown positioning
e12c3c9b55569c231b671a3d2d105d74c13d2281 eb9fda511bae78476956039326b44bb867995926 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix hidden profiles not persisting in settings
eb9fda511bae78476956039326b44bb867995926 5be7524f6cff00fd9d8d5fe23015d99e16414855 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Add search functionality to API Key Management and improve Built In Models UI
5be7524f6cff00fd9d8d5fe23015d99e16414855 05614db63b1cb00720eeaecd4c38df4cde00d737 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Replace hardcoded colors with VSCode theme variables in settings components
05614db63b1cb00720eeaecd4c38df4cde00d737 548130ebfb291ee3ae85136a2f03a671ca176200 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Replace hardcoded colors with VSCode theme variables in settings tabs and chat tools
548130ebfb291ee3ae85136a2f03a671ca176200 0af876a326f9c1746a59ff0d183cafaa909e011d LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix hiddenProfiles persistence by using profile names instead of IDs
