0000000000000000000000000000000000000000 15e2e4ba0fbb146654ede380bddc1764e4f4fa49 LaxBloxBoy2 <<EMAIL>> 1752190385 +0200	branch: Created from HEAD
15e2e4ba0fbb146654ede380bddc1764e4f4fa49 0c10b8cc98057e485b0f6a6cf0f5843648f2692c LaxBloxBoy2 <<EMAIL>> 1752190415 +0200	commit: UI improvements and settings updates
0c10b8cc98057e485b0f6a6cf0f5843648f2692c 8f5d56c51aa7712f9ccb788ff4582dc63fce4c7d LaxBloxBoy2 <<EMAIL>> 1752190732 +0200	commit: Hide UI elements as requested: profile management icons, API key inputs for BYAK profiles, diff settings, checkpoints section, and update save/close buttons design
8f5d56c51aa7712f9ccb788ff4582dc63fce4c7d 95a7cfb024fa0a09a64a186c15d545b00ae49547 LaxBloxBoy2 <<EMAIL>> 1752592438 +0200	commit: feat: Add compact MCP resource display with same design as tool display
95a7cfb024fa0a09a64a186c15d545b00ae49547 48680684bfd3e5b7addb6ff748804842e198b97a LaxBloxBoy2 <<EMAIL>> 1752620084 +0200	commit: Redesign General Settings to match reference design
48680684bfd3e5b7addb6ff748804842e198b97a 3d8aeff5522bb28a2c22dbce00ddb053de87b92c LaxBloxBoy2 <<EMAIL>> 1752628194 +0200	commit: feat: update toggle switches and MCP settings UI
3d8aeff5522bb28a2c22dbce00ddb053de87b92c c2c9665301414a40dfb671586ab6568777975d3b LaxBloxBoy2 <<EMAIL>> 1752628355 +0200	commit: feat: update toggle switches and MCP settings UI
